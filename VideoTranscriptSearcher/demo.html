<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Transcript Editor Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .word-container {
            position: relative;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 12px;
            cursor: pointer;
        }

        .time-display {
            position: absolute;
            top: -16px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 8px;
            color: #9ca3af;
            white-space: nowrap;
            background: rgba(255, 255, 255, 0.9);
            padding: 1px 2px;
            border-radius: 2px;
            border: 1px solid transparent;
            transition: all 0.2s ease;
        }

        .time-display:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        .time-display.active {
            color: #3b82f6;
            font-weight: 600;
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        .word-text {
            font-size: 18px;
            color: #374151;
            transition: all 0.2s ease;
        }

        .word-text.stick-point {
            font-weight: 600;
            color: #1f2937;
        }

        .word-container:hover .word-text {
            color: #1f2937;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Advanced Transcript Editor Demo</h1>
        
        <!-- Controls -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm text-gray-600">Duration: 1:15:2</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">Font Size:</span>
                        <button class="p-1 text-gray-500 hover:text-gray-700" onclick="changeFontSize(-2)">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                            </svg>
                        </button>
                        <span class="text-sm text-gray-600 min-w-[2rem] text-center" id="fontSize">18px</span>
                        <button class="p-1 text-gray-500 hover:text-gray-700" onclick="changeFontSize(2)">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    45 words • 3 stick points
                </div>
            </div>
            
            <!-- Text Input -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Transcript Text</label>
                <textarea 
                    rows="4" 
                    class="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Paste your transcript here. The app will automatically calculate word timings based on the duration."
                >Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source.</textarea>
            </div>
        </div>
        
        <!-- Word Timeline Editor -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Word-Level Timeline Editor</h3>
            <div class="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[200px] pt-6" id="wordContainer">
                <!-- Words will be dynamically generated here -->
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
            <h4 class="text-sm font-semibold text-blue-900 mb-2">How to use:</h4>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>• Each word shows its calculated time above it (gray by default)</li>
                <li>• Click on a time to set a specific timestamp - it becomes blue and creates a "stick point"</li>
                <li>• Hover over times to see interactive feedback</li>
                <li>• Words between stick points automatically adjust their timing</li>
                <li>• First and last words are automatically stick points at 0s and max duration</li>
            </ul>
        </div>
    </div>

    <!-- Time Input Modal -->
    <div id="timeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-96">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Set Time for "<span id="modalWord"></span>"</h3>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Time (h:m:s or m:s)</label>
                <input 
                    type="text" 
                    id="timeInput"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0:30 or 1:30:45"
                />
            </div>
            <div class="flex justify-end space-x-3">
                <button onclick="closeTimeModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button onclick="saveTime()" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">Save</button>
            </div>
        </div>
    </div>

    <script>
        const words = [
            "Contrary", "to", "popular", "belief,", "Lorem", "Ipsum", "is", "not", "simply", "random", "text.", "It", "has", "roots", "in", "a", "piece", "of", "classical", "Latin", "literature", "from", "45", "BC,", "making", "it", "over", "2000", "years", "old.", "Richard", "McClintock,", "a", "Latin", "professor", "at", "Hampden-Sydney", "College", "in", "Virginia,", "looked", "up", "one", "of", "the"
        ];
        
        let fontSize = 18;
        let stickPoints = [0, 15, 30]; // Word indices that are stick points
        let selectedWordIndex = null;
        
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }
        
        function calculateWordTime(wordIndex) {
            const totalDuration = 75; // 1:15 in seconds
            return (wordIndex / (words.length - 1)) * totalDuration;
        }
        
        function renderWords() {
            const container = document.getElementById('wordContainer');
            container.innerHTML = '';
            
            words.forEach((word, index) => {
                const wordDiv = document.createElement('div');
                wordDiv.className = 'word-container';
                
                const isStickPoint = stickPoints.includes(index);
                const wordTime = calculateWordTime(index);
                
                wordDiv.innerHTML = `
                    <div class="time-display ${isStickPoint ? 'active' : ''}" onclick="openTimeModal(${index}, '${word}')">${formatTime(wordTime)}</div>
                    <span class="word-text ${isStickPoint ? 'stick-point' : ''}" style="font-size: ${fontSize}px">${word}</span>
                `;
                
                container.appendChild(wordDiv);
            });
        }
        
        function changeFontSize(delta) {
            fontSize = Math.max(14, Math.min(26, fontSize + delta));
            document.getElementById('fontSize').textContent = fontSize + 'px';
            renderWords();
        }
        
        function openTimeModal(wordIndex, word) {
            selectedWordIndex = wordIndex;
            document.getElementById('modalWord').textContent = word;
            document.getElementById('timeInput').value = formatTime(calculateWordTime(wordIndex));
            document.getElementById('timeModal').classList.remove('hidden');
        }
        
        function closeTimeModal() {
            document.getElementById('timeModal').classList.add('hidden');
            selectedWordIndex = null;
        }
        
        function saveTime() {
            if (selectedWordIndex !== null) {
                if (!stickPoints.includes(selectedWordIndex)) {
                    stickPoints.push(selectedWordIndex);
                    stickPoints.sort((a, b) => a - b);
                }
                renderWords();
            }
            closeTimeModal();
        }
        
        // Initial render
        renderWords();
    </script>
</body>
</html>
