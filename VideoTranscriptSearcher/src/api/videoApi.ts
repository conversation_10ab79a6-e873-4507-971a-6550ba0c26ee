import apiClient from './apiClient';

export interface Video {
  id: string;
  title: string;
  description: string;
  url: string;
  thumbnailUrl: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface VideoSearchParams {
  searchText?: string;
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortDescending?: boolean;
}

export const searchVideos = async (params: VideoSearchParams = {}) => {
  const response = await apiClient.get<PaginatedResponse<Video>>('/videos/search', {
    params: {
      searchText: params.searchText || '',
      pageNumber: params.pageNumber || 1,
      pageSize: params.pageSize || 10,
      sortBy: params.sortBy || 'createdAt',
      sortDescending: params.sortDescending !== false, // default to true if not specified
    },
  });
  return response.data;
};
