import { useQuery } from '@tanstack/react-query';
import type { QueryFunction } from '@tanstack/react-query';
import { searchVideos } from '../api/videoApi';
import type { Video, VideoSearchParams, PaginatedResponse } from '../api/videoApi';

interface UseGetVideosResult {
  data?: PaginatedResponse<Video>;
  error: Error | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isFetching: boolean;
  isFetched: boolean;
  isPreviousData: boolean;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface UseVideosOptions {
  enabled?: boolean;
}

type QueryKey = readonly ['videos', VideoSearchParams];

const fetchVideos: QueryFunction<PaginatedResponse<Video>, QueryKey> = async ({ queryKey }) => {
  const [, params] = queryKey;
  return searchVideos(params);
};

export const useGetVideos = (
  params: VideoSearchParams,
  options: UseVideosOptions = {}
): UseGetVideosResult => {
  const { enabled = true } = options;

  const result = useQuery<PaginatedResponse<Video>, Error, PaginatedResponse<Video>, QueryKey>({
    queryKey: ['videos', params] as const,
    queryFn: fetchVideos,
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
    refetchOnWindowFocus: false,
  });

  const data = result.data;
  const hasNextPage = data?.hasNextPage ?? false;
  const hasPreviousPage = data?.hasPreviousPage ?? false;
  const isPreviousData = result.isFetching && !result.isLoading && !result.isError;

  return {
    data,
    error: result.error || null,
    isLoading: result.isLoading,
    isError: result.isError,
    isSuccess: result.isSuccess,
    isFetching: result.isFetching,
    isFetched: result.isFetched,
    isPreviousData,
    hasNextPage,
    hasPreviousPage,
  };
};
