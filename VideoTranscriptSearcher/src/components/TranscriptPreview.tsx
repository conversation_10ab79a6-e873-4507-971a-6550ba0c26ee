import React, { useState } from 'react';
import { FiCopy, FiDownload, FiEye, FiEyeOff } from 'react-icons/fi';

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
}

interface TranscriptPreviewProps {
  segments: TranscriptSegment[];
  duration: number;
  title: string;
}

const TranscriptPreview: React.FC<TranscriptPreviewProps> = ({
  segments,
  duration,
  title,
}) => {
  const [previewFormat, setPreviewFormat] = useState<'srt' | 'vtt' | 'txt'>('srt');
  const [showTimestamps, setShowTimestamps] = useState(true);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 1000);
    
    if (previewFormat === 'srt') {
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
    } else if (previewFormat === 'vtt') {
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
    } else {
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  const generatePreviewContent = (): string => {
    if (segments.length === 0) {
      return 'No transcript segments to preview.';
    }

    switch (previewFormat) {
      case 'srt':
        return segments
          .map((segment, index) => {
            return `${index + 1}\n${formatTime(segment.startTime)} --> ${formatTime(segment.endTime)}\n${segment.text}\n`;
          })
          .join('\n');

      case 'vtt':
        return `WEBVTT\n\n${segments
          .map((segment, index) => {
            return `${index + 1}\n${formatTime(segment.startTime)} --> ${formatTime(segment.endTime)}\n${segment.text}\n`;
          })
          .join('\n')}`;

      case 'txt':
        if (showTimestamps) {
          return segments
            .map((segment) => {
              return `[${formatTime(segment.startTime)}] ${segment.text}`;
            })
            .join('\n\n');
        } else {
          return segments
            .map((segment) => segment.text)
            .join(' ');
        }

      default:
        return '';
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatePreviewContent());
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const downloadTranscript = () => {
    const content = generatePreviewContent();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title || 'transcript'}.${previewFormat}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const previewContent = generatePreviewContent();

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
        <div>
          <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
            Transcript Preview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {segments.length} segments • {formatTime(duration)} total duration
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Format Toggle */}
          <div className="flex bg-gray-200 dark:bg-gray-600 rounded-md p-1">
            {(['srt', 'vtt', 'txt'] as const).map((format) => (
              <button
                key={format}
                onClick={() => setPreviewFormat(format)}
                className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                  previewFormat === format
                    ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                {format.toUpperCase()}
              </button>
            ))}
          </div>

          {/* Timestamp Toggle for TXT format */}
          {previewFormat === 'txt' && (
            <button
              onClick={() => setShowTimestamps(!showTimestamps)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title={showTimestamps ? 'Hide timestamps' : 'Show timestamps'}
            >
              {showTimestamps ? <FiEye className="w-4 h-4" /> : <FiEyeOff className="w-4 h-4" />}
            </button>
          )}

          {/* Action Buttons */}
          <button
            onClick={copyToClipboard}
            className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            title="Copy to clipboard"
          >
            <FiCopy className="w-4 h-4" />
          </button>
          
          <button
            onClick={downloadTranscript}
            className="p-2 text-gray-500 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
            title="Download transcript"
          >
            <FiDownload className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="p-4">
        {segments.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p className="mb-2">No transcript segments to preview</p>
            <p className="text-sm">Add segments to see the transcript preview</p>
          </div>
        ) : (
          <div className="relative">
            <pre className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4 text-sm font-mono text-gray-800 dark:text-gray-200 whitespace-pre-wrap max-h-96 overflow-y-auto">
              {previewContent}
            </pre>
            
            {/* Format Info */}
            <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-between">
                <span>
                  Format: {previewFormat.toUpperCase()}
                  {previewFormat === 'txt' && ` (${showTimestamps ? 'with' : 'without'} timestamps)`}
                </span>
                <span>
                  {previewContent.length} characters
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptPreview;
