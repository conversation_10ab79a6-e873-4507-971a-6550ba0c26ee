import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, FiEdit3, FiTrash2, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';

interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  words?: WordTiming[];
}

interface TranscriptSegmentEditorProps {
  segment: TranscriptSegment;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (segment: TranscriptSegment) => void;
  onDelete: () => void;
  duration: number;
  previousEndTime: number;
  nextStartTime: number;
}

const TranscriptSegmentEditor: React.FC<TranscriptSegmentEditorProps> = ({
  segment,
  index,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  duration,
  previousEndTime,
  nextStartTime,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(segment.text);
  const [editStartTime, setEditStartTime] = useState(segment.startTime.toString());
  const [editEndTime, setEditEndTime] = useState(segment.endTime.toString());

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  };

  const parseTimeInput = (timeStr: string): number => {
    // Handle formats like "1:23.45" or "83.45" or "123"
    if (timeStr.includes(':')) {
      const [mins, secs] = timeStr.split(':');
      return (parseInt(mins) || 0) * 60 + (parseFloat(secs) || 0);
    }
    return parseFloat(timeStr) || 0;
  };

  const handleSave = () => {
    const newStartTime = Math.max(previousEndTime, parseTimeInput(editStartTime));
    const newEndTime = Math.min(nextStartTime, Math.max(newStartTime + 0.1, parseTimeInput(editEndTime)));
    
    const updatedSegment: TranscriptSegment = {
      ...segment,
      text: editText.trim(),
      startTime: newStartTime,
      endTime: newEndTime,
    };
    
    onUpdate(updatedSegment);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditText(segment.text);
    setEditStartTime(segment.startTime.toString());
    setEditEndTime(segment.endTime.toString());
    setIsEditing(false);
  };

  const segmentDuration = segment.endTime - segment.startTime;

  return (
    <div 
      className={`border-b border-gray-200 dark:border-gray-700 transition-colors ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
      }`}
      onClick={onSelect}
    >
      <div className="p-4">
        {/* Segment Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <FiClock className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Segment {index + 1}
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {formatTime(segment.startTime)} → {formatTime(segment.endTime)}
              <span className="ml-2">({segmentDuration.toFixed(1)}s)</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!isEditing && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(true);
                  }}
                  className="p-1 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
                  title="Edit segment"
                >
                  <FiEdit3 className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  className="p-1 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
                  title="Delete segment"
                >
                  <FiTrash2 className="w-4 h-4" />
                </button>
              </>
            )}
            
            {isEditing && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSave();
                  }}
                  className="p-1 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                  title="Save changes"
                >
                  <FiCheck className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancel();
                  }}
                  className="p-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  title="Cancel editing"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        </div>

        {/* Segment Content */}
        {isEditing ? (
          <div className="space-y-3">
            {/* Time Inputs */}
            <div className="flex space-x-4">
              <div className="flex-1">
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Start Time
                </label>
                <input
                  type="text"
                  value={editStartTime}
                  onChange={(e) => setEditStartTime(e.target.value)}
                  placeholder="0:00.00 or 0.00"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  End Time
                </label>
                <input
                  type="text"
                  value={editEndTime}
                  onChange={(e) => setEditEndTime(e.target.value)}
                  placeholder="0:05.00 or 5.00"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
            
            {/* Text Input */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Transcript Text
              </label>
              <textarea
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter the transcript text for this segment..."
              />
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Transcript Text */}
            <div className="text-gray-800 dark:text-gray-200 leading-relaxed">
              {segment.text || (
                <span className="text-gray-500 dark:text-gray-400 italic">
                  No text entered for this segment
                </span>
              )}
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${Math.min(100, (segmentDuration / Math.max(duration, 1)) * 100)}%` 
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptSegmentEditor;
