import React, { useState, useRef } from 'react';
import { FiUpload, FiFile, FiAlertCircle, FiCheck, FiX } from 'react-icons/fi';
import { parseTranscriptFile, ImportResult } from '../utils/transcriptFormats';

interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  words?: WordTiming[];
}

interface TranscriptImporterProps {
  onImport: (segments: TranscriptSegment[], duration: number) => void;
  onClose: () => void;
}

const TranscriptImporter: React.FC<TranscriptImporterProps> = ({ onImport, onClose }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setImportResult(null);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content) {
        setIsProcessing(true);
        try {
          const result = parseTranscriptFile(content, file.name);
          setImportResult(result);
        } catch (error) {
          setImportResult({
            segments: [],
            duration: 0,
            errors: [`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`]
          });
        } finally {
          setIsProcessing(false);
        }
      }
    };
    
    reader.onerror = () => {
      setImportResult({
        segments: [],
        duration: 0,
        errors: ['Failed to read file']
      });
      setIsProcessing(false);
    };
    
    reader.readAsText(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const textFile = files.find(file => 
      file.type === 'text/plain' || 
      file.name.toLowerCase().endsWith('.srt') ||
      file.name.toLowerCase().endsWith('.vtt') ||
      file.name.toLowerCase().endsWith('.txt')
    );
    
    if (textFile) {
      handleFileSelect(textFile);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleImport = () => {
    if (importResult && importResult.segments.length > 0) {
      onImport(importResult.segments, importResult.duration);
      onClose();
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Import Transcript
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          {/* File Upload Area */}
          {!selectedFile && (
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragOver
                  ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
              }`}
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOver(true);
              }}
              onDragLeave={() => setIsDragOver(false)}
            >
              <FiUpload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Upload Transcript File
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Drag and drop your transcript file here, or click to browse
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Choose File
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".srt,.vtt,.txt"
                onChange={handleFileInputChange}
                className="hidden"
              />
              <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                Supported formats: SRT, VTT, TXT (with timestamps)
              </div>
            </div>
          )}

          {/* File Processing */}
          {selectedFile && isProcessing && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Processing {selectedFile.name}...
              </p>
            </div>
          )}

          {/* Import Results */}
          {selectedFile && importResult && !isProcessing && (
            <div className="space-y-4">
              {/* File Info */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <FiFile className="w-5 h-5 text-gray-500" />
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {selectedFile.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {(selectedFile.size / 1024).toFixed(1)} KB
                  </div>
                </div>
                <button
                  onClick={() => {
                    setSelectedFile(null);
                    setImportResult(null);
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>

              {/* Errors */}
              {importResult.errors.length > 0 && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <div className="flex items-center mb-2">
                    <FiAlertCircle className="w-5 h-5 text-red-500 mr-2" />
                    <h4 className="font-medium text-red-800 dark:text-red-200">
                      Import Issues ({importResult.errors.length})
                    </h4>
                  </div>
                  <div className="max-h-32 overflow-y-auto">
                    {importResult.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-700 dark:text-red-300">
                        • {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Success Summary */}
              {importResult.segments.length > 0 && (
                <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <div className="flex items-center mb-2">
                    <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                    <h4 className="font-medium text-green-800 dark:text-green-200">
                      Import Summary
                    </h4>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
                    <div>• {importResult.segments.length} segments found</div>
                    <div>• Total duration: {formatTime(importResult.duration)}</div>
                    <div>• Time range: {formatTime(importResult.segments[0]?.startTime || 0)} - {formatTime(importResult.duration)}</div>
                  </div>
                </div>
              )}

              {/* Preview */}
              {importResult.segments.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Preview (first 3 segments)
                  </h4>
                  <div className="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
                    {importResult.segments.slice(0, 3).map((segment, index) => (
                      <div key={index} className="p-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="text-sm font-mono text-blue-600 dark:text-blue-400 mb-1">
                              {formatTime(segment.startTime)} - {formatTime(segment.endTime)}
                            </div>
                            <div className="text-sm text-gray-800 dark:text-gray-200">
                              {segment.text}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    {importResult.segments.length > 3 && (
                      <div className="p-3 text-center text-sm text-gray-500 dark:text-gray-400">
                        ... and {importResult.segments.length - 3} more segments
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={handleImport}
            disabled={!importResult || importResult.segments.length === 0}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Import {importResult?.segments.length || 0} Segments
          </button>
        </div>
      </div>
    </div>
  );
};

export default TranscriptImporter;
