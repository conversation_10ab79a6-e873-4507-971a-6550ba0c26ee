/**
 * Utility functions for time calculations and formatting
 */

export interface WordTiming {
  word: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  isStickPoint?: boolean; // whether this word has a manually set time
}

/**
 * Format seconds to h:m:s format
 */
export const formatTimeHMS = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Parse h:m:s format to seconds
 */
export const parseTimeHMS = (timeStr: string): number => {
  const parts = timeStr.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 2) {
    // m:s format
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 3) {
    // h:m:s format
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  
  return 0;
};

/**
 * Calculate word timings based on total duration and word count
 */
export const calculateWordTimings = (
  text: string, 
  totalDuration: number, 
  existingStickPoints: { wordIndex: number; time: number }[] = []
): WordTiming[] => {
  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;
  
  if (wordCount === 0) return [];
  
  // Sort stick points by word index
  const sortedStickPoints = [...existingStickPoints].sort((a, b) => a.wordIndex - b.wordIndex);
  
  const wordTimings: WordTiming[] = [];
  
  // Calculate time per word for segments between stick points
  let currentWordIndex = 0;
  
  for (let i = 0; i <= sortedStickPoints.length; i++) {
    const startStickPoint = i === 0 ? { wordIndex: 0, time: 0 } : sortedStickPoints[i - 1];
    const endStickPoint = i === sortedStickPoints.length 
      ? { wordIndex: wordCount - 1, time: totalDuration }
      : sortedStickPoints[i];
    
    const segmentWordCount = endStickPoint.wordIndex - startStickPoint.wordIndex + 1;
    const segmentDuration = endStickPoint.time - startStickPoint.time;
    const timePerWord = segmentDuration / segmentWordCount;
    
    // Calculate timings for words in this segment
    for (let j = 0; j < segmentWordCount; j++) {
      const wordIndex = startStickPoint.wordIndex + j;
      const startTime = startStickPoint.time + (j * timePerWord);
      const endTime = startStickPoint.time + ((j + 1) * timePerWord);
      
      wordTimings.push({
        word: words[wordIndex],
        startTime,
        endTime,
        isStickPoint: sortedStickPoints.some(sp => sp.wordIndex === wordIndex)
      });
    }
  }
  
  return wordTimings;
};

/**
 * Update word timings when a stick point is moved
 */
export const updateWordTimingsWithStickPoint = (
  currentTimings: WordTiming[],
  wordIndex: number,
  newTime: number,
  totalDuration: number
): WordTiming[] => {
  const text = currentTimings.map(wt => wt.word).join(' ');
  
  // Get existing stick points
  const existingStickPoints = currentTimings
    .map((wt, index) => ({ wordIndex: index, time: wt.startTime, isStick: wt.isStickPoint }))
    .filter(sp => sp.isStick)
    .map(sp => ({ wordIndex: sp.wordIndex, time: sp.time }));
  
  // Update or add the new stick point
  const stickPointIndex = existingStickPoints.findIndex(sp => sp.wordIndex === wordIndex);
  if (stickPointIndex >= 0) {
    existingStickPoints[stickPointIndex].time = newTime;
  } else {
    existingStickPoints.push({ wordIndex, time: newTime });
  }
  
  // Recalculate all timings
  return calculateWordTimings(text, totalDuration, existingStickPoints);
};

/**
 * Get the closest valid time for a word based on constraints
 */
export const getValidTimeForWord = (
  wordIndex: number,
  proposedTime: number,
  currentTimings: WordTiming[],
  totalDuration: number
): number => {
  // Find previous and next stick points
  let prevStickTime = 0;
  let nextStickTime = totalDuration;
  
  for (let i = 0; i < currentTimings.length; i++) {
    if (currentTimings[i].isStickPoint) {
      if (i < wordIndex) {
        prevStickTime = Math.max(prevStickTime, currentTimings[i].startTime);
      } else if (i > wordIndex) {
        nextStickTime = Math.min(nextStickTime, currentTimings[i].startTime);
        break;
      }
    }
  }
  
  // Ensure the proposed time is within bounds
  return Math.max(prevStickTime, Math.min(nextStickTime, proposedTime));
};
