interface WordTiming {
  word: string;
  startTime: number;
  endTime: number;
}

interface TranscriptSegment {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  words?: WordTiming[];
}

export interface ImportResult {
  segments: TranscriptSegment[];
  duration: number;
  errors: string[];
}

// Parse time string to seconds
const parseTimeToSeconds = (timeStr: string): number => {
  // Handle SRT format: HH:MM:SS,mmm
  if (timeStr.includes(',')) {
    const [time, ms] = timeStr.split(',');
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds + (Number(ms) || 0) / 1000;
  }
  
  // Handle VTT format: HH:MM:SS.mmm
  if (timeStr.includes('.')) {
    const [time, ms] = timeStr.split('.');
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds + (Number(ms) || 0) / 1000;
  }
  
  // Handle simple format: MM:SS or HH:MM:SS
  const parts = timeStr.split(':').map(Number);
  if (parts.length === 2) {
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  
  return 0;
};

// Format seconds to time string
export const formatTimeToSRT = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
};

export const formatTimeToVTT = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
};

// Parse SRT format
export const parseSRT = (content: string): ImportResult => {
  const errors: string[] = [];
  const segments: TranscriptSegment[] = [];
  let maxDuration = 0;

  try {
    // Split by double newlines to get individual subtitle blocks
    const blocks = content.trim().split(/\n\s*\n/);
    
    blocks.forEach((block, index) => {
      const lines = block.trim().split('\n');
      
      if (lines.length < 3) {
        errors.push(`Block ${index + 1}: Invalid format - expected at least 3 lines`);
        return;
      }
      
      // First line should be the sequence number
      const sequenceNum = parseInt(lines[0]);
      if (isNaN(sequenceNum)) {
        errors.push(`Block ${index + 1}: Invalid sequence number`);
        return;
      }
      
      // Second line should be the timing
      const timingMatch = lines[1].match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
      if (!timingMatch) {
        errors.push(`Block ${index + 1}: Invalid timing format`);
        return;
      }
      
      const startTime = parseTimeToSeconds(timingMatch[1]);
      const endTime = parseTimeToSeconds(timingMatch[2]);
      
      if (endTime <= startTime) {
        errors.push(`Block ${index + 1}: End time must be greater than start time`);
        return;
      }
      
      // Remaining lines are the text
      const text = lines.slice(2).join(' ').trim();
      
      if (!text) {
        errors.push(`Block ${index + 1}: Empty text content`);
        return;
      }
      
      segments.push({
        id: `segment-${sequenceNum}`,
        text,
        startTime,
        endTime,
        words: []
      });
      
      maxDuration = Math.max(maxDuration, endTime);
    });
    
  } catch (error) {
    errors.push(`Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    segments: segments.sort((a, b) => a.startTime - b.startTime),
    duration: maxDuration,
    errors
  };
};

// Parse VTT format
export const parseVTT = (content: string): ImportResult => {
  const errors: string[] = [];
  const segments: TranscriptSegment[] = [];
  let maxDuration = 0;

  try {
    // Remove WEBVTT header and split by double newlines
    const cleanContent = content.replace(/^WEBVTT\s*\n/, '').trim();
    const blocks = cleanContent.split(/\n\s*\n/);
    
    blocks.forEach((block, index) => {
      const lines = block.trim().split('\n');
      
      if (lines.length < 2) {
        return; // Skip empty blocks
      }
      
      // Find the timing line (may have optional cue identifier before it)
      let timingLineIndex = 0;
      let timingMatch = lines[0].match(/(\d{2}:\d{2}:\d{2}\.\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}\.\d{3})/);
      
      if (!timingMatch && lines.length > 1) {
        timingLineIndex = 1;
        timingMatch = lines[1].match(/(\d{2}:\d{2}:\d{2}\.\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}\.\d{3})/);
      }
      
      if (!timingMatch) {
        errors.push(`Block ${index + 1}: Invalid timing format`);
        return;
      }
      
      const startTime = parseTimeToSeconds(timingMatch[1]);
      const endTime = parseTimeToSeconds(timingMatch[2]);
      
      if (endTime <= startTime) {
        errors.push(`Block ${index + 1}: End time must be greater than start time`);
        return;
      }
      
      // Remaining lines are the text
      const text = lines.slice(timingLineIndex + 1).join(' ').trim();
      
      if (!text) {
        errors.push(`Block ${index + 1}: Empty text content`);
        return;
      }
      
      segments.push({
        id: `segment-${index + 1}`,
        text,
        startTime,
        endTime,
        words: []
      });
      
      maxDuration = Math.max(maxDuration, endTime);
    });
    
  } catch (error) {
    errors.push(`Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    segments: segments.sort((a, b) => a.startTime - b.startTime),
    duration: maxDuration,
    errors
  };
};

// Parse plain text with timestamps
export const parseTimestampedText = (content: string): ImportResult => {
  const errors: string[] = [];
  const segments: TranscriptSegment[] = [];
  let maxDuration = 0;

  try {
    const lines = content.trim().split('\n');
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;
      
      // Look for patterns like [MM:SS] or [HH:MM:SS] or [MM:SS.ms]
      const timestampMatch = trimmedLine.match(/^\[(\d{1,2}:\d{2}(?:\.\d{1,3})?|\d{1,2}:\d{2}:\d{2}(?:\.\d{1,3})?)\]\s*(.+)$/);
      
      if (!timestampMatch) {
        errors.push(`Line ${index + 1}: No valid timestamp found`);
        return;
      }
      
      const startTime = parseTimeToSeconds(timestampMatch[1]);
      const text = timestampMatch[2].trim();
      
      if (!text) {
        errors.push(`Line ${index + 1}: Empty text content`);
        return;
      }
      
      // Estimate end time (5 seconds or until next segment)
      const endTime = startTime + 5; // Default 5 seconds
      
      segments.push({
        id: `segment-${index + 1}`,
        text,
        startTime,
        endTime,
        words: []
      });
      
      maxDuration = Math.max(maxDuration, endTime);
    });
    
    // Adjust end times to not overlap
    for (let i = 0; i < segments.length - 1; i++) {
      if (segments[i].endTime > segments[i + 1].startTime) {
        segments[i].endTime = segments[i + 1].startTime;
      }
    }
    
  } catch (error) {
    errors.push(`Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    segments: segments.sort((a, b) => a.startTime - b.startTime),
    duration: maxDuration,
    errors
  };
};

// Auto-detect format and parse
export const parseTranscriptFile = (content: string, filename?: string): ImportResult => {
  const trimmedContent = content.trim();
  
  // Detect format based on content and filename
  if (filename?.toLowerCase().endsWith('.srt') || trimmedContent.match(/^\d+\s*\n\d{2}:\d{2}:\d{2},\d{3}/)) {
    return parseSRT(trimmedContent);
  }
  
  if (filename?.toLowerCase().endsWith('.vtt') || trimmedContent.startsWith('WEBVTT')) {
    return parseVTT(trimmedContent);
  }
  
  if (trimmedContent.match(/^\[[\d:.,]+\]/m)) {
    return parseTimestampedText(trimmedContent);
  }
  
  // Default to timestamped text format
  return parseTimestampedText(trimmedContent);
};

// Export to SRT format
export const exportToSRT = (segments: TranscriptSegment[]): string => {
  return segments
    .map((segment, index) => {
      return `${index + 1}\n${formatTimeToSRT(segment.startTime)} --> ${formatTimeToSRT(segment.endTime)}\n${segment.text}\n`;
    })
    .join('\n');
};

// Export to VTT format
export const exportToVTT = (segments: TranscriptSegment[]): string => {
  const header = 'WEBVTT\n\n';
  const content = segments
    .map((segment, index) => {
      return `${index + 1}\n${formatTimeToVTT(segment.startTime)} --> ${formatTimeToVTT(segment.endTime)}\n${segment.text}\n`;
    })
    .join('\n');
  
  return header + content;
};

// Export to timestamped text format
export const exportToTimestampedText = (segments: TranscriptSegment[]): string => {
  return segments
    .map((segment) => {
      const timestamp = formatTimeToVTT(segment.startTime).substring(3); // Remove hours if 00:
      return `[${timestamp}] ${segment.text}`;
    })
    .join('\n\n');
};
