{"name": "videotranscriptsearcher", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "tailwind:init": "tailwindcss init", "build": "vite build", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.77.0", "axios": "^1.9.0", "dotenv": "^16.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.77.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}