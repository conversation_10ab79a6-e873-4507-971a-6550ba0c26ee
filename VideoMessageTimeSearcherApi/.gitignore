# .NET Core
bin/
obj/

# Visual Studio files
.vs/
*.user
*.userosscache
*.suo
*.userprefs
*.dbmdl
*.jfm
*.pfx
*.publishsettings

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
[Oo]ut/
msbuild.log
msbuild.err
msbuild.wrn

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Local development settings
appsettings.Development.json
appsettings.Local.json

# Logs
logs/
*.log

# Local development environment files
.env
.env.local

# IDE - JetBrains Rider
.idea/
*.sln.iml

# ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Local History for Visual Studio
.localhistory/

# BeatPulse healthcheck temp database
healthchecksdb
