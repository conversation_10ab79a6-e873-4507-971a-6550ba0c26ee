using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Controllers
{
    [ApiController]
    [Route("api/word-timings")]
    public class WordTimingsController : ControllerBase
    {
        private readonly IWordTimingService _wordTimingService;
        private readonly ILogger<WordTimingsController> _logger;

        public WordTimingsController(IWordTimingService wordTimingService, ILogger<WordTimingsController> logger)
        {
            _wordTimingService = wordTimingService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<ActionResult<WordTimingDto>> AddWordTiming([FromBody] WordTimingRequest request)
        {
            try
            {
                var result = await _wordTimingService.AddWordTimingAsync(request);
                return CreatedAtAction(nameof(GetWordTiming), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding word timing");
                return StatusCode(500, "An error occurred while adding the word timing.");
            }
        }

        [HttpPost("batch")]
        public async Task<ActionResult<IEnumerable<WordTimingDto>>> AddWordTimingsBatch([FromBody] BatchWordTimingRequest request)
        {
            try
            {
                if (request == null || !request.WordTimings.Any())
                {
                    return BadRequest("No word timings provided");
                }

                var results = await _wordTimingService.AddWordTimingsBatchAsync(request);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding word timings batch");
                return StatusCode(500, "An error occurred while adding the word timings batch.");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<WordTimingDto>> GetWordTiming(int id)
        {
            try
            {
                var result = await _wordTimingService.GetWordTimingAsync(id);
                if (result == null) return NotFound();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting word timing with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the word timing.");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteWordTiming(int id)
        {
            try
            {
                var success = await _wordTimingService.DeleteWordTimingAsync(id);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting word timing with ID {id}");
                return StatusCode(500, "An error occurred while deleting the word timing.");
            }
        }
    }
}
