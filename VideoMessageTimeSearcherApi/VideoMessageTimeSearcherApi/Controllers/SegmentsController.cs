using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Controllers
{
    [ApiController]
    [Route("api/segments")]
    public class SegmentsController : ControllerBase
    {
        private readonly ISegmentService _segmentService;
        private readonly ILogger<SegmentsController> _logger;

        public SegmentsController(ISegmentService segmentService, ILogger<SegmentsController> logger)
        {
            _segmentService = segmentService;
            _logger = logger;
        }

        [HttpPost("groups")]
        public async Task<ActionResult<WordOccurrenceDto>> CreateSegmentGroup([FromBody] CreateSegmentGroupRequest request)
        {
            try
            {
                var result = await _segmentService.CreateSegmentGroupAsync(request);
                return CreatedAtAction(nameof(GetSegmentGroup), new { id = result.SegmentGroupId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating segment group");
                return StatusCode(500, "An error occurred while creating the segment group.");
            }
        }

        [HttpGet("groups/{id}")]
        public async Task<ActionResult<WordOccurrenceDto>> GetSegmentGroup(int id)
        {
            try
            {
                var result = await _segmentService.GetSegmentGroupAsync(id);
                if (result == null) return NotFound();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting segment group with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the segment group.");
            }
        }

        [HttpDelete("groups/{id}")]
        public async Task<IActionResult> DeleteSegmentGroup(int id)
        {
            try
            {
                var success = await _segmentService.DeleteSegmentGroupAsync(id);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting segment group with ID {id}");
                return StatusCode(500, "An error occurred while deleting the segment group.");
            }
        }
    }
}
