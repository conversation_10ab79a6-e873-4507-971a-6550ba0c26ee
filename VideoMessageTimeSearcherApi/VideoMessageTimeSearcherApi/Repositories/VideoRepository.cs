using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using VideoMessageTimeSearcherApi.DbContexts;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;

namespace VideoMessageTimeSearcherApi.Repositories
{
    public class VideoRepository : IVideoRepository
    {
        private readonly AppDbContext _context;

        public VideoRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<PagedResult<Video>> SearchVideosAsync(SearchVideosRequest request)
        {
            var query = _context.Videos.AsQueryable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(request.SearchText))
            {
                var searchTerm = $"%{request.SearchText.ToLower()}%";
                query = query.Where(v => EF.Functions.ILike(v.Title, searchTerm));
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply ordering
            query = request.SortBy.ToLower() switch
            {
                "title" => request.SortDescending 
                    ? query.OrderByDescending(v => v.Title) 
                    : query.OrderBy(v => v.Title),
                "duration" => request.SortDescending 
                    ? query.OrderByDescending(v => v.DurationSeconds) 
                    : query.OrderBy(v => v.DurationSeconds),
                _ => request.SortDescending 
                    ? query.OrderByDescending(v => v.CreatedAt) 
                    : query.OrderBy(v => v.CreatedAt)
            };

            // Apply pagination
            var items = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return new PagedResult<Video>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }


        public async Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request)
        {
            var query = _context.TranscriptSegments
                .Include(ts => ts.Group)
                    .ThenInclude(g => g.Video)
                .Include(ts => ts.WordTimings)
                .AsQueryable();

            // Apply language filter if specified
            if (!string.IsNullOrEmpty(request.LanguageCode))
            {
                query = query.Where(ts => ts.LanguageCode == request.LanguageCode);
            }

            // Get all segments that might contain the search text
            var segments = await query.ToListAsync();

            // Prepare search pattern
            var searchPattern = request.MatchWholeWord 
                ? $"\\b{Regex.Escape(request.SearchText)}\\b" 
                : Regex.Escape(request.SearchText);

            var regexOptions = RegexOptions.IgnoreCase;
            if (request.MatchCase)
            {
                regexOptions = RegexOptions.None;
            }

            var regex = new Regex(searchPattern, regexOptions);

            var results = new List<WordOccurrenceDto>();

            foreach (var segment in segments)
            {
                var matches = regex.Matches(segment.Text);
                if (matches.Count > 0)
                {
                    var wordOccurrence = new WordOccurrenceDto
                    {
                        VideoId = segment.Group.VideoId,
                        VideoTitle = segment.Group.Video.Title,
                        SegmentGroupId = segment.Group.Id,
                        StartTime = segment.Group.StartTime,
                        EndTime = segment.Group.EndTime,
                        Text = segment.Text,
                        WordTimings = segment.WordTimings
                            .OrderBy(wt => wt.WordIndex)
                            .Select(wt => new WordTimingDto
                            {
                                Word = wt.Word,
                                StartTime = wt.StartTime,
                                EndTime = wt.EndTime
                            })
                            .ToList()
                    };

                    // If we have word timings, filter to only include the matching words
                    if (wordOccurrence.WordTimings.Count > 0)
                    {
                        var matchingWordIndices = new List<int>();
                        for (int i = 0; i < wordOccurrence.WordTimings.Count; i++)
                        {
                            if (regex.IsMatch(wordOccurrence.WordTimings[i].Word))
                            {
                                matchingWordIndices.Add(i);
                            }
                        }


                        // If we found matching words, add the occurrence
                        if (matchingWordIndices.Count > 0)
                        {
                            wordOccurrence.WordTimings = matchingWordIndices
                                .Select(i => wordOccurrence.WordTimings[i])
                                .ToList();
                            results.Add(wordOccurrence);
                        }
                    }
                    else
                    {
                        // If no word timings, add the whole segment
                        results.Add(wordOccurrence);
                    }
                }
            }


            // Sort by video title and then by start time
            return results
                .OrderBy(r => r.VideoTitle)
                .ThenBy(r => r.StartTime)
                .ToList();
        }


        #region Video Operations

        public async Task<Video> GetVideoByIdAsync(int id)
        {
            return await _context.Videos.FindAsync(id);
        }

        public async Task<Video> CreateVideoAsync(Video video)
        {
            _context.Videos.Add(video);
            await _context.SaveChangesAsync();
            return video;
        }

        public async Task<bool> UpdateVideoAsync(Video video)
        {
            _context.Entry(video).State = EntityState.Modified;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteVideoAsync(int id)
        {
            var video = await _context.Videos.FindAsync(id);
            if (video == null) return false;

            _context.Videos.Remove(video);
            return await _context.SaveChangesAsync() > 0;
        }

        #endregion

        #region Segment Group Operations

        public async Task<TranscriptSegmentGroup> GetSegmentGroupByIdAsync(int id)
        {
            return await _context.TranscriptSegmentGroups
                .Include(g => g.Video)
                .FirstOrDefaultAsync(g => g.Id == id);
        }

        public async Task<TranscriptSegmentGroup> CreateSegmentGroupAsync(TranscriptSegmentGroup segmentGroup)
        {
            _context.TranscriptSegmentGroups.Add(segmentGroup);
            await _context.SaveChangesAsync();
            return segmentGroup;
        }

        public async Task<bool> DeleteSegmentGroupAsync(int id)
        {
            var group = await _context.TranscriptSegmentGroups.FindAsync(id);
            if (group == null) return false;

            _context.TranscriptSegmentGroups.Remove(group);
            return await _context.SaveChangesAsync() > 0;
        }

        #endregion

        #region Segment Operations

        public async Task<TranscriptSegment> GetSegmentByIdAsync(int id)
        {
            return await _context.TranscriptSegments
                .Include(s => s.Group)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<TranscriptSegment> CreateSegmentAsync(TranscriptSegment segment)
        {
            _context.TranscriptSegments.Add(segment);
            await _context.SaveChangesAsync();
            return segment;
        }

        public async Task<bool> DeleteSegmentAsync(int id)
        {
            var segment = await _context.TranscriptSegments.FindAsync(id);
            if (segment == null) return false;

            _context.TranscriptSegments.Remove(segment);
            return await _context.SaveChangesAsync() > 0;
        }

        #endregion

        #region Word Timing Operations

        public async Task<WordTiming> GetWordTimingByIdAsync(int id)
        {
            return await _context.WordTimings
                .Include(wt => wt.Segment)
                .FirstOrDefaultAsync(wt => wt.Id == id);
        }

        public async Task<WordTiming> CreateWordTimingAsync(WordTiming wordTiming)
        {
            _context.WordTimings.Add(wordTiming);
            await _context.SaveChangesAsync();
            return wordTiming;
        }

        public async Task<bool> DeleteWordTimingAsync(int id)
        {
            var wordTiming = await _context.WordTimings.FindAsync(id);
            if (wordTiming == null) return false;

            _context.WordTimings.Remove(wordTiming);
            return await _context.SaveChangesAsync() > 0;
        }


        #endregion
    }
}
