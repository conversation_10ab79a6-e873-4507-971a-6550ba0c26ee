namespace VideoMessageTimeSearcherApi.DTOs
{
    public class CreateSegmentGroupRequest
    {
        public int VideoId { get; set; }
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public List<CreateSegmentRequest> Segments { get; set; } = new();
    }

    public class CreateSegmentRequest
    {
        public string LanguageCode { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public List<CreateWordTimingRequest> WordTimings { get; set; } = new();
    }

    public class CreateWordTimingRequest
    {
        public string Word { get; set; } = string.Empty;
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public int WordIndex { get; set; }
    }
}
