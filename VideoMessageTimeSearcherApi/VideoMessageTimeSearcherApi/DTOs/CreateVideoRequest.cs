using System.ComponentModel.DataAnnotations;

namespace VideoMessageTimeSearcherApi.DTOs
{
    public class CreateVideoRequest
    {
        [Required]
        [StringLength(200, MinimumLength = 1)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Range(0.1, double.MaxValue, ErrorMessage = "Duration must be greater than 0")]
        public double Duration { get; set; } // Changed from DurationSeconds to match UI

        [Required]
        public string Transcript { get; set; } = string.Empty;

        public List<TranscriptSegmentRequest> TranscriptSegments { get; set; } = new();

        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;
    }

    public class TranscriptSegmentRequest
    {
        [Required]
        [StringLength(1000, MinimumLength = 1)]
        public string Text { get; set; } = string.Empty;

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Start time must be non-negative")]
        public double StartTime { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "End time must be non-negative")]
        public double EndTime { get; set; }

        public List<WordTimingData> Words { get; set; } = new();
    }

    public class WordTimingData
    {
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Word { get; set; } = string.Empty;

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Start time must be non-negative")]
        public double StartTime { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "End time must be non-negative")]
        public double EndTime { get; set; }
    }
}
