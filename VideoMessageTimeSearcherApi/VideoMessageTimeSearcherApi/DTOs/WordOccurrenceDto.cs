namespace VideoMessageTimeSearcherApi.DTOs
{
    public class WordOccurrenceDto
    {
        public int VideoId { get; set; }
        public string VideoTitle { get; set; } = string.Empty;
        public int SegmentGroupId { get; set; }
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<WordTimingDto> WordTimings { get; set; } = new();
    }   
}
