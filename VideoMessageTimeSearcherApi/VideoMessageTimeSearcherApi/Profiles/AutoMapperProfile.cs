using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Profiles
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // Video mappings
            CreateMap<Video, VideoDto>();
            CreateMap<CreateVideoRequest, Video>()
                .ForMember(dest => dest.DurationSeconds, opt => opt.MapFrom(src => src.Duration));

            // Transcript segment mappings
            CreateMap<TranscriptSegmentRequest, TranscriptSegmentGroup>()
                .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.StartTime))
                .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.EndTime));

            // Segment group mappings
            CreateMap<TranscriptSegmentGroup, WordOccurrenceDto>()
                .ForMember(dest => dest.VideoTitle, opt => opt.MapFrom(src => src.Video.Title));

            // Word timing mappings
            CreateMap<WordTiming, WordTimingDto>();
        }
    }
}
