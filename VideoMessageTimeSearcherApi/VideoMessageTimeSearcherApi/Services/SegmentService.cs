using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;
using System.Linq;
using System.Threading.Tasks;

namespace VideoMessageTimeSearcherApi.Services
{
    public class SegmentService : ISegmentService
    {
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public SegmentService(IVideoRepository videoRepository, IMapper mapper)
        {
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<WordOccurrenceDto> CreateSegmentGroupAsync(CreateSegmentGroupRequest request)
        {
            // Create segment group
            var segmentGroup = new TranscriptSegmentGroup
            {
                VideoId = request.VideoId,
                StartTime = request.StartTime,
                EndTime = request.EndTime
            };

            // Save the segment group first to get its ID
            segmentGroup = await _videoRepository.CreateSegmentGroupAsync(segmentGroup);

            var wordOccurrence = new WordOccurrenceDto
            {
                VideoId = segmentGroup.VideoId,
                VideoTitle = (await _videoRepository.GetVideoByIdAsync(segmentGroup.VideoId))?.Title ?? "",
                SegmentGroupId = segmentGroup.Id,
                StartTime = segmentGroup.StartTime,
                EndTime = segmentGroup.EndTime,
                WordTimings = new List<WordTimingDto>()
            };

            // Process each segment in the request
            foreach (var segmentRequest in request.Segments)
            {
                var segment = new TranscriptSegment
                {
                    GroupId = segmentGroup.Id,
                    LanguageCode = segmentRequest.LanguageCode,
                    Text = segmentRequest.Text
                };

                // Save the segment
                segment = await _videoRepository.CreateSegmentAsync(segment);


                // Process word timings if any
                if (segmentRequest.WordTimings?.Any() == true)
                {
                    foreach (var wordTimingRequest in segmentRequest.WordTimings)
                    {
                        var wordTiming = new WordTiming
                        {
                            SegmentId = segment.Id,
                            Word = wordTimingRequest.Word,
                            StartTime = wordTimingRequest.StartTime,
                            EndTime = wordTimingRequest.EndTime,
                            WordIndex = wordTimingRequest.WordIndex
                        };

                        await _videoRepository.CreateWordTimingAsync(wordTiming);

                        wordOccurrence.WordTimings.Add(new WordTimingDto
                        {
                            Word = wordTiming.Word,
                            StartTime = wordTiming.StartTime,
                            EndTime = wordTiming.EndTime
                        });
                    }
                }
            }


            return wordOccurrence;
        }


        public async Task<WordOccurrenceDto> GetSegmentGroupAsync(int id)
        {
            // Implementation would use repository methods to get the segment group by ID
            // This is a placeholder - you'll need to implement the repository methods
            return await Task.FromResult<WordOccurrenceDto>(null);
        }

        public async Task<bool> DeleteSegmentGroupAsync(int id)
        {
            return await _videoRepository.DeleteSegmentGroupAsync(id);
        }
    }
}
