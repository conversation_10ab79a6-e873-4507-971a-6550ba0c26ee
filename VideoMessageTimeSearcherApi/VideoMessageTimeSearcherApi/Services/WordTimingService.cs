using AutoMapper;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Repositories.Interfaces;
using VideoMessageTimeSearcherApi.Services.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace VideoMessageTimeSearcherApi.Services
{
    public class WordTimingService : IWordTimingService
    {
        private readonly IVideoRepository _videoRepository;
        private readonly IMapper _mapper;

        public WordTimingService(IVideoRepository videoRepository, IMapper mapper)
        {
            _videoRepository = videoRepository;
            _mapper = mapper;
        }

        public async Task<WordTimingDto> AddWordTimingAsync(WordTimingRequest request)
        {
            var wordTiming = _mapper.Map<WordTiming>(request);
            var result = await _videoRepository.CreateWordTimingAsync(wordTiming);
            return _mapper.Map<WordTimingDto>(result);
        }

        public async Task<IEnumerable<WordTimingDto>> AddWordTimingsBatchAsync(BatchWordTimingRequest request)
        {
            var wordTimings = _mapper.Map<List<WordTiming>>(request.WordTimings);
            var results = new List<WordTimingDto>();

            // Add all word timings to the database
            foreach (var wordTiming in wordTimings)
            {
                var result = await _videoRepository.CreateWordTimingAsync(wordTiming);
                results.Add(_mapper.Map<WordTimingDto>(result));
            }

            return results;
        }

        public async Task<WordTimingDto> GetWordTimingAsync(int id)
        {
            // Implementation would use a repository method to get by ID
            // This is a placeholder - you'll need to implement the repository method
            var wordTiming = await _videoRepository.GetWordTimingByIdAsync(id);
            return wordTiming != null ? _mapper.Map<WordTimingDto>(wordTiming) : null;
        }

        public async Task<bool> DeleteWordTimingAsync(int id)
        {
            return await _videoRepository.DeleteWordTimingAsync(id);
        }
    }
}
