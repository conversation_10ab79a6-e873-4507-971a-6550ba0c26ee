using VideoMessageTimeSearcherApi.DTOs.Auth;
using VideoMessageTimeSearcherApi.Models.Identity;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface IAuthService
    {
        Task<AuthResponse> RegisterAsync(RegisterRequest request);
        Task<AuthResponse> LoginAsync(LoginRequest request);
        Task<AuthResponse> RefreshTokenAsync(string token, string refreshToken);
        Task<bool> RevokeTokenAsync(string token);
        Task<bool> AddToRoleAsync(string email, string roleName);
    }
}
