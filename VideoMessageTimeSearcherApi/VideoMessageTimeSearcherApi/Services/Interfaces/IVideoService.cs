using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface IVideoService
    {
        // Search operations
        Task<PagedResult<VideoDto>> SearchVideosAsync(SearchVideosRequest request);
        Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request);
        
        // Video operations
        Task<VideoDto> GetVideoByIdAsync(int id);
        Task<VideoDto> CreateVideoAsync(CreateVideoRequest request);
        Task<bool> UpdateVideoAsync(int id, CreateVideoRequest request);
        Task<bool> DeleteVideoAsync(int id);
    }
}
