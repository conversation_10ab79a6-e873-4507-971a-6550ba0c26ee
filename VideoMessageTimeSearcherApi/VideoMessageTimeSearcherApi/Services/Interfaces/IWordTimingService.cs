using VideoMessageTimeSearcherApi.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface IWordTimingService
    {
        Task<WordTimingDto> AddWordTimingAsync(WordTimingRequest request);
        Task<IEnumerable<WordTimingDto>> AddWordTimingsBatchAsync(BatchWordTimingRequest request);
        Task<WordTimingDto> GetWordTimingAsync(int id);
        Task<bool> DeleteWordTimingAsync(int id);
    }
}
