using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using System.Threading.Tasks;

namespace VideoMessageTimeSearcherApi.Services.Interfaces
{
    public interface ISegmentService
    {
        Task<WordOccurrenceDto> CreateSegmentGroupAsync(CreateSegmentGroupRequest request);
        Task<WordOccurrenceDto> GetSegmentGroupAsync(int id);
        Task<bool> DeleteSegmentGroupAsync(int id);
    }
}
