using System.Collections.Generic;

namespace VideoMessageTimeSearcherApi.Models
{
    public class TranscriptSegmentGroup
    {
        public int Id { get; set; }
        public int VideoId { get; set; }
        public double StartTime { get; set; }
        public double EndTime { get; set; }
        
        // Navigation properties
        public Video Video { get; set; }
        public ICollection<TranscriptSegment> TranscriptSegments { get; set; }
    }
}
