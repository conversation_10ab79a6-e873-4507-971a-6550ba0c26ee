using System.Collections.Generic;

namespace VideoMessageTimeSearcherApi.Models
{
    public class TranscriptSegment
    {
        public int Id { get; set; }
        public int GroupId { get; set; }
        public string LanguageCode { get; set; }
        public string Text { get; set; }
        
        // Navigation properties
        public TranscriptSegmentGroup Group { get; set; }
        public ICollection<WordTiming> WordTimings { get; set; }
    }
}
